import React, { useEffect } from "react";
import { Control, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
  Form,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Command,
  CommandGroup,
  CommandInput,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ServiceFormValues, Customer } from "../types";
import { getNextServiceNumber } from "@/actions/entities/services";
import { addCustomer } from "@/actions/entities/customers";
import {
  Calendar,
  Mail,
  Phone,
  Hash,
  User,
  Loader2,
  Clock,
  Plus,
  ChevronsUpDown,
  RefreshCw,
  MapPin,
} from "lucide-react";
import { getCustomersAction } from "@/actions/entities/get-customers-action";

// Customer form schema for the dialog
const customerSchema = z.object({
  name: z.string().min(1, "Nama pelanggan wajib diisi"),
  contactName: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  phone: z.string().optional(),
  address: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),
  notes: z.string().optional(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

interface CustomerInfoSectionProps {
  control: Control<ServiceFormValues>;
  isPending: boolean;
  setValue?: (name: keyof ServiceFormValues, value: any) => void;
  trigger?: (
    name?: keyof ServiceFormValues | (keyof ServiceFormValues)[]
  ) => Promise<boolean>;
}

const CustomerInfoSection: React.FC<CustomerInfoSectionProps> = ({
  control,
  isPending,
  setValue,
  trigger,
}) => {
  // State for auto-generation settings
  const [autoServiceNumber] = React.useState(true);

  // State for customers
  const [customers, setCustomers] = React.useState<Customer[]>([]);
  const [isLoadingCustomers, setIsLoadingCustomers] = React.useState(false);
  const [customerDropdownOpen, setCustomerDropdownOpen] = React.useState(false);
  const [customerSearchTerm, setCustomerSearchTerm] = React.useState("");

  // State for service number generation
  const [nextServiceNumber, setNextServiceNumber] = React.useState<string>("");
  const [isLoadingService, setIsLoadingService] = React.useState(false);

  // State for customer creation dialog
  const [customerDialogOpen, setCustomerDialogOpen] = React.useState(false);
  const [isCreatingCustomer, setIsCreatingCustomer] = React.useState(false);

  // Customer form for dialog
  const customerForm = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: "",
      contactName: "",
      email: "",
      phone: "",
      address: "",
      NIK: "",
      NPWP: "",
      notes: "",
    },
  });

  // Generate fallback example service numbers for placeholders
  const currentDate = new Date();
  const year = currentDate.getFullYear();
  const yearSuffix = String(year).slice(-2);
  const fallbackServiceNumber = `SRV-${yearSuffix}S000001`;

  // Fetch customers
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setIsLoadingCustomers(true);
        const result = await getCustomersAction();

        if (result.success && result.customers) {
          // Map the customers to include NIK and NPWP fields
          const mappedCustomers = result.customers.map((customer) => ({
            id: customer.id,
            name: customer.name,
            phone: customer.phone || "-",
            email: customer.email || "-",
            address: customer.address || "-",
            NIK: customer.NIK || "-",
            NPWP: customer.NPWP || "-",
          }));

          setCustomers(mappedCustomers);
        }
      } catch (error) {
        console.error("Error fetching customers:", error);
      } finally {
        setIsLoadingCustomers(false);
      }
    };

    fetchCustomers();
  }, []);

  // Function to fetch next service number
  const fetchNextServiceNumber = async () => {
    try {
      setIsLoadingService(true);
      const result = await getNextServiceNumber();
      if (result.success && result.nextNumber) {
        setNextServiceNumber(result.nextNumber);
      }
    } catch (error) {
      console.error("Error fetching next service number:", error);
    } finally {
      setIsLoadingService(false);
    }
  };

  // Fetch next service number when auto-generation is toggled
  useEffect(() => {
    if (autoServiceNumber && !nextServiceNumber) {
      fetchNextServiceNumber();
    }
  }, [autoServiceNumber, nextServiceNumber]);

  // Update form field when service number is generated
  useEffect(() => {
    if (nextServiceNumber && setValue) {
      setValue("serviceNumber", nextServiceNumber);
    }
  }, [nextServiceNumber, setValue]);

  // Filter customers based on search term
  const filteredCustomers = customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
      customer.phone.toLowerCase().includes(customerSearchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(customerSearchTerm.toLowerCase())
  );

  // Customer creation function
  const handleCreateCustomer = async (data: CustomerFormData) => {
    setIsCreatingCustomer(true);
    try {
      // Transform data to match the full customer schema
      const customerData = {
        ...data,
        sameAsShipping: false, // Required field
        billingAddress: data.address || "",
        shippingAddress: data.address || "",
      };

      const result = await addCustomer(customerData);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success && result.customer) {
        toast.success(result.success);

        // Add the new customer to the list
        const newCustomer: Customer = {
          id: result.customer.id,
          name: result.customer.name,
          phone: result.customer.phone || "-",
          email: result.customer.email || "-",
          address: result.customer.address || "-",
          NIK: result.customer.NIK || "-",
          NPWP: result.customer.NPWP || "-",
        };

        setCustomers((prev) => [newCustomer, ...prev]);

        // Auto-select the new customer
        if (setValue) {
          setValue("customerId", result.customer.id);
          setValue("customerEmail", result.customer.email || "");
          setValue("customerPhone", result.customer.phone || "");
          setValue("customerAddress", result.customer.address || "");
          setValue("customerName", result.customer.name);
        }

        // Trigger validation
        if (trigger) {
          trigger([
            "customerId",
            "customerEmail",
            "customerPhone",
            "customerAddress",
            "customerName",
          ]);
        }

        // Close dialog and reset form
        setCustomerDialogOpen(false);
        customerForm.reset();
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("Terjadi kesalahan saat membuat pelanggan");
    } finally {
      setIsCreatingCustomer(false);
    }
  };

  // Handle customer form submission
  const handleCustomerFormSubmit =
    customerForm.handleSubmit(handleCreateCustomer);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Service Number */}
        <FormField
          control={control}
          name="serviceNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Hash className="h-4 w-4 text-purple-600" />
                Nomor Servis
              </FormLabel>
              <div className="relative">
                <FormControl>
                  <Input
                    placeholder={
                      autoServiceNumber
                        ? isLoadingService
                          ? "Loading..."
                          : `Auto - ${nextServiceNumber || fallbackServiceNumber}`
                        : `Contoh: SRV-${yearSuffix}S000001`
                    }
                    {...field}
                    disabled={isPending || autoServiceNumber}
                    value={autoServiceNumber ? "" : field.value}
                    onChange={(e) => {
                      if (!autoServiceNumber) {
                        field.onChange(e.target.value);
                      }
                    }}
                    className={autoServiceNumber ? "bg-gray-50 pr-12" : "pr-12"}
                  />
                </FormControl>
                {/* Generate Button inside input */}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={fetchNextServiceNumber}
                  disabled={isPending || isLoadingService}
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-8 w-8 p-0 bg-green-50 hover:bg-green-100 border border-green-200 text-green-700"
                >
                  {isLoadingService ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : (
                    <RefreshCw className="h-3 w-3" />
                  )}
                </Button>
              </div>
              <FormDescription>
                {autoServiceNumber
                  ? "Nomor servis dibuat otomatis oleh sistem"
                  : "Masukkan nomor servis manual"}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Customer Dropdown */}
        <FormField
          control={control}
          name="customerId"
          render={({ field }) => {
            return (
              <FormItem className="col-span-1">
                <FormLabel className="flex items-center gap-1.5">
                  <User className="h-4 w-4 text-blue-600" />
                  Pelanggan <span className="text-red-500 font-bold">*</span>
                </FormLabel>
                <div className="flex gap-2">
                  <Popover
                    open={customerDropdownOpen}
                    onOpenChange={setCustomerDropdownOpen}
                  >
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={customerDropdownOpen}
                          className={`flex-1 justify-between hover:bg-accent hover:text-accent-foreground cursor-pointer ${
                            !field.value ? "text-muted-foreground" : ""
                          }`}
                          disabled={isPending}
                        >
                          {isLoadingCustomers ? (
                            <div className="flex items-center">
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Memuat pelanggan...
                            </div>
                          ) : field.value ? (
                            <div className="flex items-center">
                              <User className="mr-2 h-4 w-4" />
                              <span>
                                {customers.find(
                                  (customer) => customer.id === field.value
                                )?.name || "Pilih pelanggan"}
                              </span>
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <User className="mr-2 h-4 w-4" />
                              <span>Pilih pelanggan</span>
                            </div>
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                      <Command shouldFilter={false}>
                        <CommandInput
                          placeholder="Cari pelanggan..."
                          value={customerSearchTerm}
                          onValueChange={setCustomerSearchTerm}
                          className="h-9"
                          onFocus={() => {
                            if (!customerDropdownOpen) {
                              setCustomerDropdownOpen(true);
                            }
                          }}
                        />
                        <CommandList className="max-h-[300px] overflow-y-auto">
                          <CommandGroup>
                            {/* Show all filtered customers or just 3 if not searching */}
                            {(customerSearchTerm
                              ? filteredCustomers
                              : customers.slice(0, 3)
                            ).map((customer) => (
                              <div key={customer.id} className="px-2">
                                <button
                                  type="button"
                                  className="w-full flex items-center text-left rounded-md px-3 py-3 text-sm bg-transparent hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors border border-transparent hover:border-gray-200 focus:bg-accent focus:text-accent-foreground focus:outline-none"
                                  onClick={() => {
                                    field.onChange(customer.id);
                                    setCustomerDropdownOpen(false);
                                    setCustomerSearchTerm("");

                                    // Auto-populate email and phone
                                    if (setValue) {
                                      setValue(
                                        "customerEmail",
                                        customer.email !== "-"
                                          ? customer.email
                                          : ""
                                      );
                                      setValue(
                                        "customerPhone",
                                        customer.phone !== "-"
                                          ? customer.phone
                                          : ""
                                      );
                                      setValue(
                                        "customerAddress",
                                        customer.address !== "-"
                                          ? customer.address
                                          : ""
                                      );
                                      setValue("customerName", customer.name);
                                    }

                                    // Trigger validation
                                    if (trigger) {
                                      trigger([
                                        "customerEmail",
                                        "customerPhone",
                                        "customerAddress",
                                        "customerName",
                                      ]);
                                    }
                                  }}
                                >
                                  <div className="flex-1">
                                    <div className="font-medium text-gray-900 dark:text-gray-100">
                                      {customer.name}
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                      {customer.phone} • {customer.email}
                                    </div>
                                  </div>
                                </button>
                              </div>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>

                  {/* Add Customer Button */}
                  <Dialog
                    open={customerDialogOpen}
                    onOpenChange={setCustomerDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        className="shrink-0 cursor-pointer"
                        type="button"
                        disabled={isPending}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
                      <DialogHeader>
                        <DialogTitle>Tambah Pelanggan Baru</DialogTitle>
                        <DialogDescription>
                          Buat pelanggan baru untuk servis ini.
                        </DialogDescription>
                      </DialogHeader>
                      <Form {...customerForm}>
                        <form
                          onSubmit={handleCustomerFormSubmit}
                          className="space-y-4"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Customer Name */}
                            <FormField
                              control={customerForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>
                                    Nama Pelanggan{" "}
                                    <span className="text-red-500">*</span>
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nama pelanggan"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Contact Name */}
                            <FormField
                              control={customerForm.control}
                              name="contactName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nama Kontak</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nama kontak"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {/* Email */}
                            <FormField
                              control={customerForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Email"
                                      type="email"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            {/* Phone */}
                            <FormField
                              control={customerForm.control}
                              name="phone"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Nomor Telepon</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Nomor telepon"
                                      {...field}
                                      disabled={isCreatingCustomer}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          {/* Address */}
                          <FormField
                            control={customerForm.control}
                            name="address"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Alamat</FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Alamat pelanggan"
                                    className="resize-none"
                                    {...field}
                                    disabled={isCreatingCustomer}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <div className="flex justify-end gap-2 pt-4">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() => {
                                setCustomerDialogOpen(false);
                                customerForm.reset();
                              }}
                              disabled={isCreatingCustomer}
                            >
                              Batal
                            </Button>
                            <Button
                              type="submit"
                              disabled={isCreatingCustomer}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              {isCreatingCustomer ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Menyimpan...
                                </>
                              ) : (
                                "Simpan Pelanggan"
                              )}
                            </Button>
                          </div>
                        </form>
                      </Form>
                    </DialogContent>
                  </Dialog>
                </div>
                <FormMessage />
              </FormItem>
            );
          }}
        />

        {/* Customer Email and Phone */}
        <div className="col-span-1 grid grid-cols-1 gap-2 md:grid-cols-2">
          {/* Email */}
          <FormField
            control={control}
            name="customerEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Mail className="h-4 w-4 text-green-600" />
                  Email
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Email pelanggan"
                    type="email"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Phone */}
          <FormField
            control={control}
            name="customerPhone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="flex items-center gap-1.5">
                  <Phone className="h-4 w-4 text-orange-600" />
                  Nomor Handphone
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Nomor handphone pelanggan"
                    {...field}
                    disabled={isPending}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Transaction Date */}
        <FormField
          control={control}
          name="transactionDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Calendar className="h-4 w-4 text-green-600" />
                Tgl. Transaksi
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : new Date()}
                  setDate={(date) => onChange(date || new Date())}
                  placeholder="Pilih tanggal transaksi"
                  disabled={isPending}
                  className="w-full cursor-pointer"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Due Date */}
        <FormField
          control={control}
          name="dueDate"
          render={({ field: { value, onChange } }) => (
            <FormItem>
              <FormLabel className="flex items-center gap-1.5">
                <Clock className="h-4 w-4 text-red-600" />
                Tgl. Jatuh Tempo
              </FormLabel>
              <FormControl>
                <DatePicker
                  date={value ? new Date(value) : undefined}
                  setDate={(date) => onChange(date)}
                  placeholder="Pilih tanggal jatuh tempo"
                  disabled={isPending}
                  className="w-full cursor-pointer"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Customer Address */}
      <FormField
        control={control}
        name="customerAddress"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center gap-1.5">
              <MapPin className="h-4 w-4 text-blue-600" />
              Alamat Pelanggan
            </FormLabel>
            <FormControl>
              <Textarea
                placeholder="Alamat pelanggan"
                className="resize-none"
                {...field}
                disabled={isPending}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default CustomerInfoSection;
